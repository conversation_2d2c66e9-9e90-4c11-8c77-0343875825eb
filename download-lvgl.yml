# LVGL Download Configuration
# This file defines how to download and setup LVGL 8.3.10 for ESP32 project

lvgl_config:
  version: "8.3.10"
  source_type: "git"
  repository: "https://github.com/lvgl/lvgl.git"
  tag: "v8.3.10"
  target_directory: "components/lvgl"
  
download_commands:
  # Method 1: Using git clone
  git_clone:
    - "git clone --branch v8.3.10 --depth 1 https://github.com/lvgl/lvgl.git components/lvgl"
    - "cd components/lvgl && git checkout v8.3.10"
  
  # Method 2: Using ESP-IDF component manager
  idf_component:
    - "idf.py add-dependency lvgl/lvgl==8.3.10"
    - "idf.py reconfigure"
  
  # Method 3: Using wget/curl to download release archive
  download_archive:
    - "mkdir -p components/lvgl"
    - "curl -L https://github.com/lvgl/lvgl/archive/refs/tags/v8.3.10.tar.gz -o lvgl-8.3.10.tar.gz"
    - "tar -xzf lvgl-8.3.10.tar.gz --strip-components=1 -C components/lvgl"
    - "rm lvgl-8.3.10.tar.gz"

setup_commands:
  # Commands to run after downloading LVGL
  post_download:
    - "echo 'LVGL 8.3.10 downloaded successfully'"
    - "ls -la components/lvgl"
    - "idf.py reconfigure"
    - "idf.py build"

verification:
  # Verify LVGL installation
  check_files:
    - "components/lvgl/lvgl.h"
    - "components/lvgl/src/core/lv_obj.h"
    - "components/lvgl/CMakeLists.txt"
  
  check_commands:
    - "test -f components/lvgl/lvgl.h && echo 'LVGL header found'"
    - "grep -q '8.3.10' components/lvgl/lv_conf_template.h && echo 'Version verified'"

# Alternative: PowerShell commands for Windows
windows_commands:
  powershell:
    - "git clone --branch v8.3.10 --depth 1 https://github.com/lvgl/lvgl.git components/lvgl"
    - "if (Test-Path 'components/lvgl/lvgl.h') { Write-Host 'LVGL downloaded successfully' }"
    - "idf.py reconfigure"
